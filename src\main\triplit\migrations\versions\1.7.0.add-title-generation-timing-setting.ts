/** biome-ignore-all lint/suspicious/noExplicitAny: ignore all */
import { FieldMigration } from "@shared/triplit/migrations/base-migration";
import type {
  CollectionMigrationConfig,
  DataFillFunction,
  MigrationContext,
} from "@shared/triplit/migrations/types";

export class AddTitleGenerationTimingSettingMigration extends FieldMigration {
  public readonly version = "1.7.0";
  public readonly description =
    "Add title generation timing setting to settings collection";
  public readonly dependencies: string[] = [];

  private fillTitleGenerationTimingSetting: DataFillFunction = async (
    _client,
    entity,
    context,
  ) => {
    const { logger } = context;

    // Set default values for model selection settings
    const titleGenerationTiming = "first-round"; // Default to use first round

    logger.debug(
      `Setting default title generation timing setting for settings entity ${entity.id}`,
    );

    return {
      titleGenerationTiming,
    };
  };

  protected getMigrationConfig(): CollectionMigrationConfig[] {
    return [
      {
        collectionName: "settings",
        fields: [
          {
            fieldName: "titleGenerationTiming",
            fillFunction: this.fillTitleGenerationTimingSetting,
          },
        ],
        customMigration: async (context: MigrationContext) => {
          const { logger } = context;
          logger.info(
            "Running custom migration logic for title generation timing setting",
          );
          logger.info("Title generation will use first time by default");
        },
      },
    ];
  }
}
