import { Label } from "@renderer/components/ui/field";
import { Select } from "@renderer/components/ui/select";
import { cn } from "@renderer/lib/utils";
import { useTitleGenerationTimingSetting } from "@renderer/queries/hooks/use-settings";
import { useTranslation } from "react-i18next";

const { settingsService } = window.service;

export function TitleGenerationTiming() {
  const { t } = useTranslation("translation", {
    keyPrefix: "settings.preference-settings.title-generation-timing",
  });

  const { data: titleGenerationTiming } = useTitleGenerationTimingSetting();

  const titleGenerationTimingOptions = [
    { key: "first-round", label: t("first-time") },
    { key: "every-round", label: t("every-time") },
    { key: "off", label: t("off") },
  ];

  const handleTitleGenerationTimingChange = async (
    newTitleGenerationTiming: string,
  ) => {
    await settingsService.setTitleGenerationTiming(newTitleGenerationTiming);
  };

  return (
    <div className="flex flex-col gap-2">
      <Label className="text-label-fg">{t("label")}</Label>
      <Select
        className="min-w-full"
        selectedKey={titleGenerationTiming}
        onSelectionChange={(key) =>
          handleTitleGenerationTimingChange(key as string)
        }
        aria-label="Title Generation Timing"
      >
        <Select.Trigger className="inset-ring-transparent h-11 rounded-[10px] bg-setting text-setting-fg transition-none hover:inset-ring-transparent" />
        <Select.List
          className="min-w-full"
          items={titleGenerationTimingOptions}
        >
          {({ key, label }) => (
            <Select.Option
              className={cn(
                "flex cursor-pointer justify-between",
                "[&>[data-slot='check-indicator']]:order-last [&>[data-slot='check-indicator']]:mr-0 [&>[data-slot='check-indicator']]:ml-auto",
              )}
              key={key}
              id={key}
              textValue={label}
            >
              <span className="flex items-center gap-2">
                <span className="text-sm">{label}</span>
              </span>
            </Select.Option>
          )}
        </Select.List>
      </Select>
    </div>
  );
}
